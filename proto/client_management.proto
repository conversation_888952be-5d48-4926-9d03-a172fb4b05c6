syntax = "proto3";

package client_management;

// Service for managing client connections and mappings
service ClientManagementService {
  // Store a new client mapping
  rpc SetClient(SetClientRequest) returns (SetClientResponse);
  
  // Retrieve client information
  rpc GetClient(GetClientRequest) returns (GetClientResponse);
  
  // Store a new session mapping
  rpc SetSession(SetSessionRequest) returns (SetSessionResponse);

  // Retrieve session information
  rpc GetSessionMapping(GetSessionMappingRequest) returns (GetSessionMappingResponse);
  
  // Validate client exists and is active
  rpc ValidateClient(ValidateClientRequest) returns (ValidateClientResponse);
}

// Client mapping messages
message SetClientRequest {
  string client_id = 1;
  string connection_info = 2; // JSON metadata about connection
  int64 created_at = 3;
}

message SetClientResponse {
  bool success = 1;
  string message = 2;
}

message GetClientRequest {
  string client_id = 1;
}

message GetClientResponse {
  string client_id = 1;
  string connection_info = 2;
  int64 created_at = 3;
  bool found = 4;
}

// Session mapping messages
message SetSessionRequest {
  string session_id = 1;
  string client_id = 2;
  string user_id = 3;
  int64 created_at = 4;
}

message SetSessionResponse {
  bool success = 1;
  string message = 2;
}

message GetSessionMappingRequest {
  string session_id = 1;
}

message GetSessionMappingResponse {
  string session_id = 1;
  string client_id = 2;
  string user_id = 3;
  int64 created_at = 4;
  bool found = 5;
}

// Validation messages
message ValidateClientRequest {
  string client_id = 1;
}

message ValidateClientResponse {
  bool valid = 1;
  string message = 2;
}
