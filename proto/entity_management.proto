syntax = "proto3";

package entity_management;

// Service for managing game entities within maps
service EntityManagementService {
  // Assign an entity ID to a client for a specific map
  rpc AssignEntity(AssignEntityRequest) returns (AssignEntityResponse);
  
  // Reset/remove entity assignment for a client
  rpc ResetEntity(ResetEntityRequest) returns (ResetEntityResponse);
  
  // List all entities in a map
  rpc ListEntities(ListEntitiesRequest) returns (ListEntitiesResponse);
  
  // Get entity information
  rpc GetEntity(GetEntityRequest) returns (GetEntityResponse);
  
  // Join a map (creates player entity and assigns ID)
  rpc JoinMap(JoinMapRequest) returns (JoinMapResponse);
  
  // Leave a map (removes player entity)
  rpc LeaveMap(LeaveMapRequest) returns (LeaveMapResponse);
}

// Entity assignment messages
message AssignEntityRequest {
  string client_id = 1;
  uint32 map_id = 2;
  EntityType entity_type = 3;
  EntityData entity_data = 4;
}

message AssignEntityResponse {
  bool success = 1;
  uint64 entity_id = 2;
  string message = 3;
}

message ResetEntityRequest {
  string client_id = 1;
  uint32 map_id = 2;
}

message ResetEntityResponse {
  bool success = 1;
  string message = 2;
}

// Entity listing messages
message ListEntitiesRequest {
  uint32 map_id = 1;
  optional EntityType entity_type = 2; // Filter by type if provided
}

message ListEntitiesResponse {
  repeated EntityInfo entities = 1;
}

message GetEntityRequest {
  uint64 entity_id = 1;
  uint32 map_id = 2;
}

message GetEntityResponse {
  bool found = 1;
  EntityInfo entity = 2;
}

// Map join/leave messages
message JoinMapRequest {
  string client_id = 1;
  uint32 map_id = 2;
  float spawn_x = 3;
  float spawn_y = 4;
  float spawn_z = 5;
}

message JoinMapResponse {
  bool success = 1;
  uint64 entity_id = 2;
  string message = 3;
}

message LeaveMapRequest {
  string client_id = 1;
  uint32 map_id = 2;
}

message LeaveMapResponse {
  bool success = 1;
  string message = 2;
}

// Common entity data structures
message EntityInfo {
  uint64 entity_id = 1;
  string client_id = 2;
  uint32 map_id = 3;
  EntityType entity_type = 4;
  EntityData entity_data = 5;
  int64 created_at = 6;
}

message EntityData {
  float x = 1;
  float y = 2;
  float z = 3;
  int32 hp = 4;
  int32 max_hp = 5;
  string name = 6;
  // Additional fields can be added as needed
}

enum EntityType {
  ENTITY_TYPE_UNSPECIFIED = 0;
  ENTITY_TYPE_PLAYER = 1;
  ENTITY_TYPE_NPC = 2;
  ENTITY_TYPE_MOB = 3;
  ENTITY_TYPE_ITEM = 4;
}
