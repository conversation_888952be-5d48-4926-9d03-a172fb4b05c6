use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{debug, error};
use utils::redis_cache::{Cache, RedisCache};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ClientMapping {
    pub client_id: String,
    pub connection_info: String,
    pub created_at: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SessionMapping {
    pub session_id: String,
    pub client_id: String,
    pub user_id: String,
    pub created_at: i64,
}

pub struct ClientManagementRepository {
    cache: Arc<Mutex<RedisCache>>,
}

impl ClientManagementRepository {
    pub fn new(cache: Arc<Mutex<RedisCache>>) -> Self {
        Self { cache }
    }

    // Client mapping operations
    pub async fn set_client(&self, client_mapping: &ClientMapping) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let cache_key = format!("client:{}", client_mapping.client_id);
        
        debug!("Setting client mapping: {:?}", client_mapping);
        
        self.cache
            .lock()
            .await
            .set(&cache_key, client_mapping, 3600) // 1 hour TTL
            .await
            .map_err(|e| {
                error!("Failed to set client mapping: {}", e);
                Box::new(e) as Box<dyn std::error::Error + Send + Sync>
            })?;
        
        Ok(())
    }

    pub async fn get_client(&self, client_id: &str) -> Result<Option<ClientMapping>, Box<dyn std::error::Error + Send + Sync>> {
        let cache_key = format!("client:{}", client_id);
        
        debug!("Getting client mapping for: {}", client_id);
        
        match self.cache.lock().await.get::<ClientMapping>(&cache_key).await {
            Ok(client) => Ok(client),
            Err(e) => {
                error!("Failed to get client mapping: {}", e);
                Err(Box::new(e) as Box<dyn std::error::Error + Send + Sync>)
            }
        }
    }

    pub async fn validate_client(&self, client_id: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        match self.get_client(client_id).await? {
            Some(_) => Ok(true),
            None => Ok(false),
        }
    }

    // Session mapping operations
    pub async fn set_session(&self, session_mapping: &SessionMapping) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let cache_key = format!("session:{}", session_mapping.session_id);
        
        debug!("Setting session mapping: {:?}", session_mapping);
        
        self.cache
            .lock()
            .await
            .set(&cache_key, session_mapping, 3600) // 1 hour TTL
            .await
            .map_err(|e| {
                error!("Failed to set session mapping: {}", e);
                Box::new(e) as Box<dyn std::error::Error + Send + Sync>
            })?;
        
        Ok(())
    }

    pub async fn get_session(&self, session_id: &str) -> Result<Option<SessionMapping>, Box<dyn std::error::Error + Send + Sync>> {
        let cache_key = format!("session:{}", session_id);
        
        debug!("Getting session mapping for: {}", session_id);
        
        match self.cache.lock().await.get::<SessionMapping>(&cache_key).await {
            Ok(session) => Ok(session),
            Err(e) => {
                error!("Failed to get session mapping: {}", e);
                Err(Box::new(e) as Box<dyn std::error::Error + Send + Sync>)
            }
        }
    }

    // Optional: Entity mapping operations (for persistence)
    pub async fn set_entity_mapping(&self, map_id: u32, client_id: &str, entity_id: u64) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let cache_key = format!("entity:{}:{}", map_id, client_id);
        
        debug!("Setting entity mapping: map_id={}, client_id={}, entity_id={}", map_id, client_id, entity_id);
        
        self.cache
            .lock()
            .await
            .set(&cache_key, &entity_id, 1800) // 30 minutes TTL
            .await
            .map_err(|e| {
                error!("Failed to set entity mapping: {}", e);
                Box::new(e) as Box<dyn std::error::Error + Send + Sync>
            })?;
        
        Ok(())
    }

    pub async fn get_entity_mapping(&self, map_id: u32, client_id: &str) -> Result<Option<u64>, Box<dyn std::error::Error + Send + Sync>> {
        let cache_key = format!("entity:{}:{}", map_id, client_id);
        
        debug!("Getting entity mapping for: map_id={}, client_id={}", map_id, client_id);
        
        match self.cache.lock().await.get::<u64>(&cache_key).await {
            Ok(entity_id) => Ok(entity_id),
            Err(e) => {
                error!("Failed to get entity mapping: {}", e);
                Err(Box::new(e) as Box<dyn std::error::Error + Send + Sync>)
            }
        }
    }

    pub async fn remove_entity_mapping(&self, map_id: u32, client_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let cache_key = format!("entity:{}:{}", map_id, client_id);
        
        debug!("Removing entity mapping for: map_id={}, client_id={}", map_id, client_id);
        
        self.cache
            .lock()
            .await
            .delete(&cache_key)
            .await
            .map_err(|e| {
                error!("Failed to remove entity mapping: {}", e);
                Box::new(e) as Box<dyn std::error::Error + Send + Sync>
            })?;
        
        Ok(())
    }
}
