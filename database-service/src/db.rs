use crate::characters::CharacterRepository;
use crate::sessions::SessionRepository;
use crate::users::UserRepository;
use crate::client_management::ClientManagementRepository;
use sqlx::PgPool;
use std::sync::Arc;
use tokio::sync::Mutex;
use utils::redis_cache::RedisCache;

pub struct Database {
    pub user_repo: Arc<UserRepository>,
    pub character_repo: Arc<CharacterRepository>,
    pub session_repo: Arc<SessionRepository>,
    pub client_management_repo: Arc<ClientManagementRepository>,
}

impl Database {
    pub fn new(pool: PgPool, redis_cache: Arc<Mutex<RedisCache>>) -> Self {
        let user_repo = Arc::new(UserRepository::new(pool.clone(), redis_cache.clone()));
        let character_repo = Arc::new(CharacterRepository::new(pool.clone(), redis_cache.clone()));
        let session_repo = Arc::new(SessionRepository::new(pool.clone(), redis_cache.clone()));
        let client_management_repo = Arc::new(ClientManagementRepository::new(redis_cache));

        Self {
            user_repo,
            character_repo,
            session_repo,
            client_management_repo,
        }
    }
}
