use crate::client_management::{ClientMapping, SessionMapping};
use crate::db::Database;
use std::sync::Arc;
use tonic::{Request, Response, Status};
use tracing::{debug, error, info};

pub mod client_management {
    tonic::include_proto!("client_management");
}

use client_management::client_management_service_server::ClientManagementService;
use client_management::*;

pub struct MyClientManagementService {
    pub db: Arc<Database>,
}

#[tonic::async_trait]
impl ClientManagementService for MyClientManagementService {
    async fn set_client(
        &self,
        request: Request<SetClientRequest>,
    ) -> Result<Response<SetClientResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Setting client: {}", req.client_id);
        
        let client_mapping = ClientMapping {
            client_id: req.client_id.clone(),
            connection_info: req.connection_info,
            created_at: req.created_at,
        };
        
        match self.db.client_management_repo.set_client(&client_mapping).await {
            Ok(_) => {
                info!("Successfully set client mapping for: {}", req.client_id);
                Ok(Response::new(SetClientResponse {
                    success: true,
                    message: "Client mapping stored successfully".to_string(),
                }))
            }
            Err(e) => {
                error!("Failed to set client mapping: {}", e);
                Ok(Response::new(SetClientResponse {
                    success: false,
                    message: format!("Failed to store client mapping: {}", e),
                }))
            }
        }
    }

    async fn get_client(
        &self,
        request: Request<GetClientRequest>,
    ) -> Result<Response<GetClientResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Getting client: {}", req.client_id);
        
        match self.db.client_management_repo.get_client(&req.client_id).await {
            Ok(Some(client)) => {
                debug!("Found client mapping for: {}", req.client_id);
                Ok(Response::new(GetClientResponse {
                    client_id: client.client_id,
                    connection_info: client.connection_info,
                    created_at: client.created_at,
                    found: true,
                }))
            }
            Ok(None) => {
                debug!("Client mapping not found for: {}", req.client_id);
                Ok(Response::new(GetClientResponse {
                    client_id: req.client_id,
                    connection_info: String::new(),
                    created_at: 0,
                    found: false,
                }))
            }
            Err(e) => {
                error!("Failed to get client mapping: {}", e);
                Err(Status::internal(format!("Failed to get client mapping: {}", e)))
            }
        }
    }

    async fn set_session(
        &self,
        request: Request<SetSessionRequest>,
    ) -> Result<Response<SetSessionResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Setting session: {}", req.session_id);
        
        let session_mapping = SessionMapping {
            session_id: req.session_id.clone(),
            client_id: req.client_id,
            user_id: req.user_id,
            created_at: req.created_at,
        };
        
        match self.db.client_management_repo.set_session(&session_mapping).await {
            Ok(_) => {
                info!("Successfully set session mapping for: {}", req.session_id);
                Ok(Response::new(SetSessionResponse {
                    success: true,
                    message: "Session mapping stored successfully".to_string(),
                }))
            }
            Err(e) => {
                error!("Failed to set session mapping: {}", e);
                Ok(Response::new(SetSessionResponse {
                    success: false,
                    message: format!("Failed to store session mapping: {}", e),
                }))
            }
        }
    }

    async fn get_session_mapping(
        &self,
        request: Request<GetSessionMappingRequest>,
    ) -> Result<Response<GetSessionMappingResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Getting session: {}", req.session_id);
        
        match self.db.client_management_repo.get_session(&req.session_id).await {
            Ok(Some(session)) => {
                debug!("Found session mapping for: {}", req.session_id);
                Ok(Response::new(GetSessionMappingResponse {
                    session_id: session.session_id,
                    client_id: session.client_id,
                    user_id: session.user_id,
                    created_at: session.created_at,
                    found: true,
                }))
            }
            Ok(None) => {
                debug!("Session mapping not found for: {}", req.session_id);
                Ok(Response::new(GetSessionMappingResponse {
                    session_id: req.session_id,
                    client_id: String::new(),
                    user_id: String::new(),
                    created_at: 0,
                    found: false,
                }))
            }
            Err(e) => {
                error!("Failed to get session mapping: {}", e);
                Err(Status::internal(format!("Failed to get session mapping: {}", e)))
            }
        }
    }

    async fn validate_client(
        &self,
        request: Request<ValidateClientRequest>,
    ) -> Result<Response<ValidateClientResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Validating client: {}", req.client_id);
        
        match self.db.client_management_repo.validate_client(&req.client_id).await {
            Ok(valid) => {
                debug!("Client validation result for {}: {}", req.client_id, valid);
                Ok(Response::new(ValidateClientResponse {
                    valid,
                    message: if valid {
                        "Client is valid".to_string()
                    } else {
                        "Client not found or invalid".to_string()
                    },
                }))
            }
            Err(e) => {
                error!("Failed to validate client: {}", e);
                Ok(Response::new(ValidateClientResponse {
                    valid: false,
                    message: format!("Validation error: {}", e),
                }))
            }
        }
    }
}
