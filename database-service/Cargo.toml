[package]
name = "database-service"
version = "0.1.0"
edition = "2021"

[features]
mocks = []
consul = []

[dependencies]
tokio = { version = "1.41.1", features = ["full"] }
sqlx = { version = "0.8.2", features = ["postgres", "runtime-tokio-native-tls", "chrono"] }
tonic = "0.12.3"
chrono = { version = "0.4.39", features = ["serde"] }
serde = { version = "1.0", features = ["derive"] }
dotenv = "0.15"
tracing = "0.1"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "chrono"] }
prost = "0.13.3"
serde_json = "1.0.133"
async-trait = "0.1.83"
utils = { path = "../utils" }
tonic-health = "0.12.3"
log = "0.4.26"
uuid = { version = "1.11.0", features = ["v4"] }
redis = "0.27.5"
deadpool-redis = "0.18.0"

[build-dependencies]
tonic-build = "0.12.3"
