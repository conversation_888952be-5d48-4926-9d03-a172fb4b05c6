fn main() {
    tonic_build::configure()
        .build_server(true)
        .compile_well_known_types(true)
        .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
        .compile_protos(
            &[
                "../proto/user_db_api.proto",
                "../proto/character_db_api.proto",
                "../proto/session_db_api.proto",
                "../proto/client_management.proto",
                "../proto/entity_management.proto",
            ],
            &["../proto"],
        )
        .unwrap_or_else(|e| panic!("Failed to compile protos {:?}", e));
}
