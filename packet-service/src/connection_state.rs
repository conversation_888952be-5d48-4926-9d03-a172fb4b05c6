use std::collections::HashMap;
use std::sync::Arc;
use std::fmt;
use tokio::net::TcpStream;
use tokio::io::{WriteHalf};

use crate::handlers::chat_client::ChatClient<PERSON><PERSON><PERSON>;

#[derive(Clone)]
pub struct ConnectionState {
    pub user_id: Option<String>,
    pub session_id: Option<String>,
    pub character_id: Option<i8>,
    pub character_name: Option<String>,
    pub character_list: Option<Vec<u32>>,
    pub additional_data: HashMap<String, String>, // Flexible data storage
    pub client_id: Option<String>, // UUID client ID from database
    pub local_id: u16, // Local connection ID for packet routing
    pub chat_handler: Option<Arc<ChatClientHandler>>,
    pub writer: Option<Arc<tokio::sync::Mutex<WriteHalf<TcpStream>>>>,
}

impl ConnectionState {
    pub fn new() -> Self {
        Self {
            user_id: None,
            session_id: None,
            character_id: None,
            character_name: None,
            character_list: None,
            additional_data: HashMap::new(),
            client_id: None,
            local_id: 0,
            chat_handler: None,
            writer: None,
        }
    }
}

impl fmt::Debug for ConnectionState {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("ConnectionState")
            .field("user_id", &self.user_id)
            .field("session_id", &self.session_id)
            .field("character_id", &self.character_id)
            .field("character_name", &self.character_name)
            .field("character_list", &self.character_list)
            .field("additional_data", &self.additional_data)
            .field("client_id", &self.client_id)
            .field("local_id", &self.local_id)
            .field("chat_handler", &self.chat_handler.as_ref().map(|_| "<chat handler>"))
            .field("writer", &self.writer.as_ref().map(|_| "<writer>"))
            .finish()
    }
}