use crate::character_client::CharacterClient;
use crate::connection_service::ConnectionService;
use crate::packet::{send_packet, Packet, PacketPayload};
use crate::packet_type::PacketType;
use chrono::{Local, Timelike};
use std::error::Error;
use std::sync::Arc;
use tokio::io::WriteHalf;
use tokio::net::TcpStream;
use tokio::sync::Mutex;
use tonic::transport::Channel;
use tracing::{debug, error};
use utils::null_string::NullTerminatedString;
use utils::service_discovery::get_kube_service_endpoints_by_dns;
use crate::handlers::chat_client::chat::{ChatMessage, MessageType};
use crate::handlers::chat_client::ChatClientHandler;

pub async fn create_chat_client_handler(
    stream_for_task: Arc<Mutex<WriteHalf<TcpStream>>>,
    task_chat_handler: Arc<ChatClientHandler>,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::srv_normal_chat::SrvNormalChat;
    use crate::packets::srv_shout_chat::SrvShoutChat;
    use crate::packets::srv_party_chat::SrvPartyChat;
    use crate::packets::srv_whisper_chat::SrvWhisperChat;
    use crate::packets::srv_clan_chat::SrvClanChat;
    use crate::packets::srv_allied_chat::SrvAlliedChat;
    tokio::spawn({
        async move {
            debug!("Spawning chat handler task");
            loop {
                let mut rx = task_chat_handler.inbound_rx.lock().await;
                while let Some(chat_msg) = rx.recv().await {
                    debug!("Packet-Service received chat message: {} (client_id: {}, type {})", chat_msg.message, chat_msg.client_id, chat_msg.r#type);
                    match chat_msg.r#type {
                        1 => {
                            // Normal Chat
                            let data = SrvNormalChat {
                                char_id: chat_msg.client_id.parse().unwrap(),
                                message: NullTerminatedString(chat_msg.message),
                            };

                            // Send the packet to the client
                            let response_packet = Packet::new(PacketType::PakwcNormalChat, &data);
                            debug!("Attempting to send normal chat to client");
                            debug!("Locking stream");
                            let mut locked_stream = stream_for_task.lock().await;
                            debug!("Locked stream");
                            if let Err(e) = send_packet(&mut locked_stream, &response_packet.unwrap()).await
                            {
                                error!("unable to send normal chat: {:?}", e);
                            }
                        }
                        2 => {
                            // Shout Chat
                            let data = SrvShoutChat {
                                sender: NullTerminatedString(chat_msg.sender),
                                message: NullTerminatedString(chat_msg.message),
                            };
                            let response_packet = Packet::new(PacketType::PakwcShoutChat, &data);
                            debug!("Attempting to send shout chat to client");
                            debug!("Locking stream");
                            let mut locked_stream = stream_for_task.lock().await;
                            debug!("Locked stream");
                            if let Err(e) = send_packet(&mut locked_stream, &response_packet.unwrap()).await
                            {
                                error!("unable to send shout chat: {:?}", e);
                            }
                        }
                        3 => {
                            // Party Chat
                            let data = SrvPartyChat {
                                char_id: chat_msg.client_id.parse().unwrap(),
                                message: NullTerminatedString(chat_msg.message),
                            };
                            let response_packet = Packet::new(PacketType::PakwcPartyChat, &data);
                            debug!("Attempting to send party chat to client");
                            debug!("Locking stream");
                            let mut locked_stream = stream_for_task.lock().await;
                            debug!("Locked stream");
                            if let Err(e) = send_packet(&mut locked_stream, &response_packet.unwrap()).await
                            {
                                error!("unable to send party chat: {:?}", e);
                            }
                        }
                        4 => {
                            // Whisper Chat
                            let data = SrvWhisperChat {
                                sender: NullTerminatedString(chat_msg.sender),
                                message: NullTerminatedString(chat_msg.message),
                            };
                            let response_packet = Packet::new(PacketType::PakwcWhisperChat, &data);
                            debug!("Attempting to send whisper chat to client");
                            debug!("Locking stream");
                            let mut locked_stream = stream_for_task.lock().await;
                            debug!("Locked stream");
                            if let Err(e) = send_packet(&mut locked_stream, &response_packet.unwrap()).await
                            {
                                error!("unable to send whisper chat: {:?}", e);
                            }
                        }
                        5 => {
                            // Clan Chat
                            let data = SrvClanChat {
                                sender: NullTerminatedString(chat_msg.sender),
                                message: NullTerminatedString(chat_msg.message),
                            };
                            let response_packet = Packet::new(PacketType::PakwcClanChat, &data);
                            debug!("Attempting to send clan chat to client");
                            debug!("Locking stream");
                            let mut locked_stream = stream_for_task.lock().await;
                            debug!("Locked stream");
                            if let Err(e) = send_packet(&mut locked_stream, &response_packet.unwrap()).await
                            {
                                error!("unable to send clan chat: {:?}", e);
                            }
                        }
                        6 => {
                            // Allied Chat
                            let data = SrvAlliedChat {
                                team: 0,
                                message: NullTerminatedString(chat_msg.message),
                            };
                            let response_packet = Packet::new(PacketType::PakwcAlliedChat, &data);
                            debug!("Attempting to send allied chat to client");
                            debug!("Locking stream");
                            let mut locked_stream = stream_for_task.lock().await;
                            debug!("Locked stream");
                            if let Err(e) = send_packet(&mut locked_stream, &response_packet.unwrap()).await
                            {
                                error!("unable to send allied chat: {:?}", e);
                            }
                        }
                        _ => {
                            // Normal Chat
                            let data = SrvNormalChat {
                                char_id: chat_msg.client_id.parse().unwrap(),
                                message: NullTerminatedString(chat_msg.message),
                            };

                            // Send the packet to the client
                            let response_packet = Packet::new(PacketType::PakwcNormalChat, &data);
                            debug!("Locking stream");
                            let mut locked_stream = stream_for_task.lock().await;
                            debug!("Locked stream");
                            if let Err(e) = send_packet(&mut locked_stream, &response_packet.unwrap()).await
                            {
                                error!("unable to send normal chat: {:?}", e);
                            }
                        }
                    }
                }
            }
            debug!("Chat handler task exiting");
        }
    });

    Ok(())
}

pub(crate) async fn handle_normal_chat(
    packet: Packet,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::cli_normal_chat::*;
    use crate::packets::srv_normal_chat::*;
    let request = CliNormalChat::decode(packet.payload.as_slice())?;
    debug!("{:?}", request);

    if let Some(mut state) = connection_service.get_connection(&connection_id) {
        let user_id = state.user_id.clone().expect("Missing user id in connection state");
        let message = ChatMessage {
            client_id: state.client_id.clone().unwrap_or_default(),
            r#type: MessageType::Normal as i32,
            message: request.message.clone().0,
            target_id: "".to_string(),
            sender: state.character_name.clone().unwrap_or_default(),
        };
        state.chat_handler.unwrap().send_message(message).await;
    }
    Ok(())
}

pub(crate) async fn handle_shout_chat(
    packet: Packet,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::cli_shout_chat::*;
    use crate::packets::srv_shout_chat::*;
    let request = CliShoutChat::decode(packet.payload.as_slice())?;
    debug!("{:?}", request);

    if let Some(mut state) = connection_service.get_connection(&connection_id) {
        let message = ChatMessage {
            client_id: state.client_id.clone().unwrap_or_default(),
            r#type: MessageType::Shout as i32,
            message: request.message.clone().0,
            target_id: "".to_string(),
            sender: state.character_name.clone().unwrap_or_default(),
        };
        state.chat_handler.unwrap().send_message(message).await;
    }
    Ok(())
}
