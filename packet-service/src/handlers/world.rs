use crate::character_client::CharacterClient;
use crate::connection_service::ConnectionService;
use crate::packet::{send_packet, Packet, PacketPayload};
use crate::packet_type::PacketType;
use crate::world_client::WorldClientManager;
use chrono::{Local, Timelike};
use std::error::Error;
use std::sync::Arc;
use tokio::net::TcpStream;
use tokio::sync::Mutex;
use tonic::transport::Channel;
use tracing::{debug, error, info, warn};
use utils::service_discovery::get_kube_service_endpoints_by_dns;

fn distance(x1: f64, y1: f64, x2: f64, y2: f64) -> u16 {
    let dist = ((x2 - x1).powi(2) + (y2 - y1).powi(2)).sqrt();
    dist.round() as u16
}

pub(crate) async fn handle_change_map_req(
    packet: Packet,
    character_client: Arc<Mutex<CharacterClient>>,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
    world_client_manager: Arc<WorldClientManager>,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::cli_change_map_req::*;
    use crate::packets::srv_change_map_reply::*;
    let request = CliChangeMapReq::decode(packet.payload.as_slice())?;
    debug!("{:?}", request);

    let mut user_id = "".to_string();
    let mut char_id = 0;
    let mut client_id: u16 = 0;
    let mut character_id_list: Vec<u32> = Vec::new();
    let session_id;
    if let Some(mut state) = connection_service.get_connection(&connection_id) {
        user_id = state.user_id.expect("Missing user id in connection state");
        session_id = state.session_id.expect("Missing session id in connection state");
        char_id = state.character_id.expect("Missing character id in connection state");
        character_id_list = state.character_list.clone().expect("Missing character id list");
        client_id = state.client_id.clone().unwrap_or_default().parse::<u16>().unwrap_or(0);
    }

    let mut character_client = character_client.lock().await;
    let character_data = character_client
        .get_character(&user_id.to_string(), character_id_list[char_id as usize] as u8)
        .await?;

    let character = character_data.character.unwrap_or_default();
    let stats = character.stats.unwrap();

    let now = Local::now();
    let time_as_u16 = (now.hour() * 100 + now.minute()) as u16;

    // if let Err(e) = world_client_manager
    //     .send_client_map_change_event(&*session_id, request.map_id as u32)
    //     .await
    // {
    //     warn!("Failed to send map change event to world service: {}", e);
    //     // Don't return error as the map change itself was successful
    // }
    // 
    // debug!("Sent map change event for client {} to world service: map {}", session_id, request.map_id);

    let data = SrvChangeMapReply {
        object_index: client_id,
        hp: stats.hp as u16,
        mp: stats.mp as u16,
        xp: stats.xp as u16,
        penalize_xp: stats.penalty_xp as u16,
        craft_rate: 0,
        update_time: 0,
        world_rate: 1,
        town_rate: 1,
        item_rate: [0u8; 11],
        flags: 1,
        world_time: time_as_u16,
        team_number: 10,
    };
    let response_packet = Packet::new(PacketType::PakwcChangeMapReply, &data)?;
    if let Some(mut state) = connection_service.get_connection_mut(&connection_id) {
        let writer_clone = state.writer.clone().unwrap();
        let mut locked_stream = writer_clone.lock().await;
        send_packet(&mut locked_stream, &response_packet).await?;
    }

    Ok(())
}

pub(crate) async fn handle_mouse_cmd_req(
    packet: Packet,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
    world_client_manager: Arc<WorldClientManager>,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::cli_mouse_cmd::*;
    use crate::packets::srv_mouse_cmd::*;
    let request = CliMouseCmd::decode(packet.payload.as_slice())?;
    debug!("{:?}", request);

    let mut char_id = 0;
    let mut client_id: u16 = 0;
    let mut character_id_list: Vec<u32> = Vec::new();
    let mut session_id = "".to_string();
    if let Some(mut state) = connection_service.get_connection(&connection_id) {
        char_id = state.character_id.expect("Missing character id in connection state");
        character_id_list = state.character_list.clone().expect("Missing character id list");
        client_id = state.client_id.clone().unwrap_or_default().parse::<u16>().unwrap_or(0);
        session_id = state.session_id.clone().expect("Missing session id in connection state");
    }

    if let Err(e) = world_client_manager
        .send_client_move_event(&*session_id, request.x, request.y, request.z as f32)
        .await
    {
        error!("Failed to send move event to world service: {}", e);
        return Err(e);
    }

    debug!("Sent move event for client {} to world service: ({}, {}, {})",
                   session_id, request.x, request.y, request.z);

    let data = SrvMouseCmd {
        id: client_id,
        target_id: request.target_id,
        distance: distance(520000 as f64, 520000 as f64, request.x as f64, request.y as f64),
        x: request.x,
        y: request.y,
        z: request.z,
    };
    let response_packet = Packet::new(PacketType::PakwcMouseCmd, &data)?;
    if let Some(mut state) = connection_service.get_connection_mut(&connection_id) {
        let writer_clone = state.writer.clone().unwrap();
        let mut locked_stream = writer_clone.lock().await;
        send_packet(&mut locked_stream, &response_packet).await?;
    }
    Ok(())
}

// World service integration - movement events

pub(crate) async fn handle_togggle_move_req(
    packet: Packet,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::cli_toggle_move::*;
    use crate::packets::srv_toggle_move::*;
    let request = CliToggleMove::decode(packet.payload.as_slice())?;
    debug!("{:?}", request);

    let mut client_id: u16 = 0;
    if let Some(mut state) = connection_service.get_connection(&connection_id) {
        client_id = state.client_id.clone().unwrap_or_default().parse::<u16>().unwrap_or(0);
    }
    let mut toggle_type = 0;;
    {
        use crate::packets::cli_toggle_move::ToggleMove;
        match request.type_ {
            ToggleMove::Run => { toggle_type = 2; }
            ToggleMove::Sit => { toggle_type = 8; }
            ToggleMove::Drive => { toggle_type = 9; }
        }
    }

    use crate::packets::srv_toggle_move::ToggleMove;
    let mut final_type = ToggleMove::Run;
    {
        match toggle_type {
            0 => { final_type = ToggleMove::Run; }
            1 => { final_type = ToggleMove::Sit; }
            2 => { final_type = ToggleMove::Drive; }
            _ => { final_type = ToggleMove::Run; }
        }
    }

    let data = SrvToggleMove {
        object_id: client_id,
        type_: final_type,
        run_speed: 0,
    };
    let response_packet = Packet::new(PacketType::PakwcToggleMove, &data)?;
    if let Some(mut state) = connection_service.get_connection_mut(&connection_id) {
        let writer_clone = state.writer.clone().unwrap();
        let mut locked_stream = writer_clone.lock().await;
        send_packet(&mut locked_stream, &response_packet).await?;
    }
    Ok(())
}

pub(crate) async fn handle_set_animation_req(
    packet: Packet,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::cli_set_animation::*;
    use crate::packets::srv_set_animation::*;
    let request = CliSetAnimation::decode(packet.payload.as_slice())?;
    debug!("{:?}", request);

    let mut client_id: u16 = 0;
    if let Some(mut state) = connection_service.get_connection(&connection_id) {
        client_id = state.client_id.clone().unwrap_or_default().parse::<u16>().unwrap_or(0);
    }

    let data = SrvSetAnimation {
        id: request.id,
        value: request.value,
        object_id: client_id,
    };
    let response_packet = Packet::new(PacketType::PacwcSetAnimation, &data)?;
    if let Some(mut state) = connection_service.get_connection_mut(&connection_id) {
        let writer_clone = state.writer.clone().unwrap();
        let mut locked_stream = writer_clone.lock().await;
        send_packet(&mut locked_stream, &response_packet).await?;
    }
    Ok(())
}

// World service integration - movement events

async fn handle_world_events(
    world_client_manager: Arc<WorldClientManager>,
    connection_service: Arc<ConnectionService>,
    session_id: String,
) {
    info!("Starting world event handler for session {}", session_id);

    loop {
        match world_client_manager.receive_world_event(&session_id).await {
            Some(world_event) => {
                // debug!("Received world event for session {}: {:?}", session_id, world_event);

                // Process the world event and convert to game packets
                match world_event.event {
                    Some(crate::world_client::world::world_event::Event::NpcSpawn(npc_spawn)) => {
                        debug!("Processing NPC spawn event: {:?}", npc_spawn);
                        // Convert to the appropriate game packet and send to client
                        // This would involve creating a spawn packet and sending it through the connection's writer
                    }
                    Some(crate::world_client::world::world_event::Event::MobSpawn(mob_spawn)) => {
                        debug!("Processing mob spawn event: {:?}", mob_spawn);
                        // Convert to the appropriate game packet and send to client
                    }
                    Some(crate::world_client::world::world_event::Event::ObjectDespawn(despawn)) => {
                        debug!("Processing object despawn event: {:?}", despawn);
                        // Convert to the appropriate game packet and send to client
                    }
                    Some(crate::world_client::world::world_event::Event::NearbyUpdate(nearby_update)) => {
                        debug!("Processing nearby objects update: {} objects", nearby_update.objects.len());
                        // Convert to the appropriate game packet and send to client

                        let npcs = nearby_update.objects.iter().filter(|obj| obj.object_type == 2).collect::<Vec<_>>();
                        let mobs = nearby_update.objects.iter().filter(|obj| obj.object_type == 3).collect::<Vec<_>>();

                        debug!("There are {} npcs and {} mobs", npcs.len(), mobs.len());
                        debug!("Nearby npcs: {:?}", npcs);
                        debug!("Nearby mobs: {:?}", mobs);

                        // if !npcs.is_empty() {
                        //     // Send all nearby npcs in a single batch update
                        //     let batch_event = SrvNpcChar {
                        //         id: 0,
                        //         x: 0.0,
                        //         y: 0.0,
                        //         dest_x: 0.0,
                        //         dest_y: 0.0,
                        //         command: 0,
                        //         target_id: 0,
                        //         move_mode: 0,
                        //         hp: 0,
                        //         team_id: 0,
                        //         status_flag: 0,
                        //         npc_id: 0,
                        //         quest_id: 0,
                        //         angle: 0.0,
                        //         event_status: 0,
                        //     };
                        //     let response_packet = Packet::new(PacketType::PakwcNpcChar, &batch_event)?;
                        //     if let Some(mut state) = connection_service.get_connection_mut(&session_id) {
                        //         let writer_clone = state.writer.clone().unwrap();
                        //         let mut locked_stream = writer_clone.lock().await;
                        //         send_packet(&mut locked_stream, &response_packet).await?;
                        //     }
                        // }
                    }
                    None => {
                        warn!("Received world event with no event data for session {}", session_id);
                    }
                }
            }
            None => {
                debug!("World event stream ended for session {}", session_id);
                break;
            }
        }
    }

    info!("World event handler ended for session {}", session_id);
}

// Helper function to start world event handling for a client
pub fn start_world_event_handler(
    world_client_manager: Arc<WorldClientManager>,
    connection_service: Arc<ConnectionService>,
    session_id: String,
) {
    tokio::spawn(async move {
        handle_world_events(world_client_manager, connection_service, session_id).await;
    });
}