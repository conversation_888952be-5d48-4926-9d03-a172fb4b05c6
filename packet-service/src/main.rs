use crate::auth_client::AuthClient;
use crate::bufferpool::BufferPool;
use crate::character_client::CharacterClient;
use crate::connection_service::ConnectionService;
use crate::metrics::{ACTIVE_CONNECTIONS, PACKETS_RECEIVED};
use crate::packet::Packet;
use crate::router::PacketRouter;
use crate::world_client::WorldClientManager;
use dotenv::dotenv;
use prometheus::{self, Encoder, TextEncoder};
use prometheus_exporter;
use std::collections::HashMap;
use std::env;
use std::error::Error;
use std::net::ToSocketAddrs;
use std::str::FromStr;
use std::sync::Arc;
use tokio::io::AsyncReadExt;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{Mutex, Semaphore, oneshot};
use tokio::time::{timeout, Duration};
use tokio::{io, select, signal};
use tracing::Level;
use tracing::{debug, error, info, warn};
use tracing_subscriber::EnvFilter;
use utils::service_discovery::get_kube_service_endpoints_by_dns;
use utils::{health_check, logging};
use warp::Filter;

mod auth_client;
mod bufferpool;
mod character_client;
mod connection_service;
mod connection_state;
mod database_client;
mod dataconsts;
mod enums;
mod handlers;
mod metrics;
mod packet;
mod packet_type;
mod packets;
mod router;
mod types;
mod interceptors;
mod id_manager;
mod world_client;

pub mod common {
    tonic::include_proto!("common");
}
pub mod auth {
    tonic::include_proto!("auth");
}
pub mod character_common {
    tonic::include_proto!("character_common");
}
pub mod character {
    tonic::include_proto!("character");
}
pub mod client_management {
    tonic::include_proto!("client_management");
}

const BUFFER_POOL_SIZE: usize = 1000;
const MAX_CONCURRENT_CONNECTIONS: usize = 100;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    dotenv().ok();
    let app_name = env!("CARGO_PKG_NAME");
    logging::setup_logging(app_name, &["packet_service", "health_check"]);

    // Set the gRPC server address
    let addr = env::var("LISTEN_ADDR").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = env::var("SERVICE_PORT").unwrap_or_else(|_| "29000".to_string());
    let metrics_port = env::var("PACKET_METRICS_PORT").unwrap_or_else(|_| "4001".to_string());
    let auth_url = format!(
        "http://{}",
        get_kube_service_endpoints_by_dns("auth-service", "tcp", "auth-service")
            .await?
            .get(0)
            .unwrap()
    );
    let character_url = format!(
        "http://{}",
        get_kube_service_endpoints_by_dns("character-service", "tcp", "character-service")
            .await?
            .get(0)
            .unwrap()
    );
    let world_url = format!(
        "http://{}",
        get_kube_service_endpoints_by_dns("world-service", "tcp", "world-service")
            .await?
            .get(0)
            .unwrap()
    );
    let database_url = format!(
        "http://{}",
        get_kube_service_endpoints_by_dns("database-service", "tcp", "database-service")
            .await?
            .get(0)
            .unwrap()
    );

    // Start health-check endpoint
    health_check::start_health_check(addr.as_str()).await?;

    let auth_client = Arc::new(Mutex::new(AuthClient::connect(&auth_url).await?));
    let character_client = Arc::new(Mutex::new(CharacterClient::connect(&character_url).await?));
    let world_client_manager = Arc::new(WorldClientManager::new());
    let connection_service = Arc::new(ConnectionService::new(&database_url).await?);

    let full_addr = format!("{}:{}", &addr, port);

    // Create shutdown signal channel for the TCP listener
    let (tcp_shutdown_tx, mut tcp_shutdown_rx) = oneshot::channel::<()>();

    let tcp_server_task = tokio::spawn(async move {
        let semaphore = Arc::new(Semaphore::new(MAX_CONCURRENT_CONNECTIONS));
        let listener = TcpListener::bind(full_addr.clone()).await.unwrap();
        let buffer_pool = BufferPool::new(BUFFER_POOL_SIZE);

        let packet_router = PacketRouter {
            auth_client,
            character_client,
            connection_service,
            world_client_manager,
            world_url,
        };

        info!("Packet service listening on {}", full_addr);

        loop {
            tokio::select! {
                // Check for shutdown signal
                _ = &mut tcp_shutdown_rx => {
                    info!("TCP server received shutdown signal");
                    break;
                }
                // Accept new connections
                result = listener.accept() => {
                    match result {
                        Ok((mut socket, addr)) => {
                            let packet_router = packet_router.clone();
                            info!("New connection from {}", addr);

                            let pool = buffer_pool.clone();
                            let permit = semaphore.clone().acquire_owned().await.unwrap();
                            let peer_addr = socket.peer_addr().unwrap();
                            let (mut reader, writer) = io::split(socket);
                            let writer = Arc::new(tokio::sync::Mutex::new(writer));

                            // Spawn a new task for each connection
                            tokio::spawn(async move {
                                let _permit = permit;
                                match packet_router.connection_service.add_connection(writer, peer_addr.to_string()).await {
                                    Ok(connection_id) => {
                                        if let Err(e) = packet_router
                                            .handle_connection(reader, pool, connection_id.clone(), peer_addr.to_string())
                                            .await
                                        {
                                            error!("Error handling connection: {}", e);
                                        }
                                        packet_router.connection_service.remove_connection(&connection_id);
                                    }
                                    Err(e) => {
                                        error!("Failed to add connection: {}", e);
                                    }
                                }
                            });
                        }
                        Err(e) => {
                            error!("Failed to accept connection: {}", e);
                        }
                    }
                }
            }
        }
        info!("TCP server shut down gracefully");
    });

    let binding = format!("{}:{}", &addr, metrics_port);
    prometheus_exporter::start(binding.parse().unwrap()).unwrap();

    // Wait for shutdown signal
    info!("Packet service is running. Waiting for shutdown signal...");
    utils::signal_handler::wait_for_signal().await;

    info!("Shutdown signal received. Beginning graceful shutdown...");

    // Signal the TCP server to stop accepting new connections
    if let Err(_) = tcp_shutdown_tx.send(()) {
        warn!("Failed to send shutdown signal to TCP server (receiver may have been dropped)");
    }

    // Wait for the TCP server to finish with a timeout
    match timeout(Duration::from_secs(30), tcp_server_task).await {
        Ok(result) => {
            if let Err(e) = result {
                error!("TCP server task failed: {}", e);
            } else {
                info!("Packet service shut down successfully");
            }
        }
        Err(_) => {
            error!("TCP server shutdown timed out after 30 seconds");
        }
    }

    Ok(())
}
