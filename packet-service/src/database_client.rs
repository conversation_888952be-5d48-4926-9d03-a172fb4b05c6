use std::sync::Arc;
use tonic::transport::{Channel, Endpoint};
use tonic::{Request, Status};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

pub mod client_management {
    tonic::include_proto!("client_management");
}

use client_management::client_management_service_client::ClientManagementServiceClient;
use client_management::*;

#[derive(Clone)]
pub struct DatabaseClient {
    client: ClientManagementServiceClient<Channel>,
}

impl DatabaseClient {
    pub async fn new(database_url: &str) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        info!("Connecting to database service at: {}", database_url);
        
        let endpoint = Endpoint::from_shared(database_url.to_string())?
            .connect_timeout(std::time::Duration::from_secs(5))
            .timeout(std::time::Duration::from_secs(30));
            
        let channel = endpoint.connect().await?;
        let client = ClientManagementServiceClient::new(channel);
        
        info!("Successfully connected to database service");
        
        Ok(Self { client })
    }

    pub async fn store_client(&mut self, client_id: &str, connection_info: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        debug!("Storing client mapping for: {}", client_id);
        
        let request = Request::new(SetClientRequest {
            client_id: client_id.to_string(),
            connection_info: connection_info.to_string(),
            created_at: chrono::Utc::now().timestamp(),
        });

        match self.client.set_client(request).await {
            Ok(response) => {
                let resp = response.into_inner();
                if resp.success {
                    info!("Successfully stored client mapping for: {}", client_id);
                    Ok(true)
                } else {
                    warn!("Failed to store client mapping: {}", resp.message);
                    Ok(false)
                }
            }
            Err(e) => {
                error!("gRPC error storing client mapping: {}", e);
                Err(Box::new(e))
            }
        }
    }

    pub async fn validate_client(&mut self, client_id: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        debug!("Validating client: {}", client_id);
        
        let request = Request::new(ValidateClientRequest {
            client_id: client_id.to_string(),
        });

        match self.client.validate_client(request).await {
            Ok(response) => {
                let resp = response.into_inner();
                debug!("Client validation result for {}: {}", client_id, resp.valid);
                Ok(resp.valid)
            }
            Err(e) => {
                error!("gRPC error validating client: {}", e);
                Err(Box::new(e))
            }
        }
    }

    pub async fn get_client(&mut self, client_id: &str) -> Result<Option<(String, String, i64)>, Box<dyn std::error::Error + Send + Sync>> {
        debug!("Getting client info for: {}", client_id);
        
        let request = Request::new(GetClientRequest {
            client_id: client_id.to_string(),
        });

        match self.client.get_client(request).await {
            Ok(response) => {
                let resp = response.into_inner();
                if resp.found {
                    Ok(Some((resp.client_id, resp.connection_info, resp.created_at)))
                } else {
                    Ok(None)
                }
            }
            Err(e) => {
                error!("gRPC error getting client: {}", e);
                Err(Box::new(e))
            }
        }
    }
}

pub fn generate_client_id() -> String {
    Uuid::new_v4().to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_client_id() {
        let id1 = generate_client_id();
        let id2 = generate_client_id();
        
        // Should be different UUIDs
        assert_ne!(id1, id2);
        
        // Should be valid UUID format
        assert!(Uuid::parse_str(&id1).is_ok());
        assert!(Uuid::parse_str(&id2).is_ok());
    }
}
