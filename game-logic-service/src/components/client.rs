
#[derive(Debug)]
pub struct Client {
    pub client_id: u16,
    pub access_level: u16,
    pub session_id: String
}

impl Default for Client {
    fn default() -> Self {
        Self { client_id: 0, access_level: 1, session_id: "".to_string() }
    }
}

impl Client {
    pub fn get_client_id(&self) -> u16 {
        self.client_id
    }

    pub fn get_access_level(&self) -> u16 {
        self.access_level
    }
    
    pub fn get_session_id(&self) -> &str { &self.session_id }
}