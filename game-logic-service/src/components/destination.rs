#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Destination {
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub dist: f32, // Distance to destination
}

impl Default for Destination {
    fn default() -> Self {
        Self { x: 520000.0, y: 520000.0, z: 1.0, dist: 0.0 }
    }
}

impl Destination {
    pub fn new(x: f32, y: f32, z: f32, distance: f32) -> Self {
        Self { x, y, z, dist: distance }
    }
}