use std::sync::{<PERSON>, Mutex};
use std::time::{Duration, Instant};
use hecs::{Entity, World};
use tracing::debug;
use crate::components::basic_info::BasicInfo;
use crate::components::destination::Destination;
use crate::components::position::Position;
use crate::components::markers::*;
use crate::components::life::Life;
use crate::spatial_grid::SpatialGrid;
use crate::id_manager::IdManager;
use crate::loader::Zone;
use crate::world_client::WorldGameLogicServiceImpl;

/// EntitySystem manages the game world and provides thread-safe access to entity operations
pub struct EntitySystem {
    world: Arc<Mutex<World>>,
    spatial_grid: Arc<Mutex<SpatialGrid>>,
    id_manager: Arc<Mutex<IdManager>>,
    world_service: Option<Arc<WorldGameLogicServiceImpl>>,
    last_movement_update: Arc<Mutex<Instant>>,
}

impl EntitySystem {
    pub fn new() -> Self {
        let now = Instant::now();
        Self {
            world: Arc::new(Mutex::new(World::new())),
            spatial_grid: Arc::new(Mutex::new(SpatialGrid::new())),
            id_manager: Arc::new(Mutex::new(IdManager::new())),
            world_service: None,
            last_movement_update: Arc::new(Mutex::new(now)),
        }
    }

    pub fn with_world_service(mut self, world_service: Arc<WorldGameLogicServiceImpl>) -> Self {
        self.world_service = Some(world_service);
        self
    }

    /// Load a map zone into the world
    pub fn load_map(&self, zone: Zone) {
        let mut world = self.world.lock().unwrap();
        let mut spatial_grid = self.spatial_grid.lock().unwrap();
        let id_manager = self.id_manager.clone();

        // Create a temporary factory to load the map
        let mut temp_factory = crate::entity_factory::EntityFactory {
            world: &mut *world,
            spatial_grid: std::mem::take(&mut *spatial_grid),
            id_manager,
            world_service: self.world_service.clone(),
        };

        temp_factory.load_map(zone);

        // Put the spatial grid back
        *spatial_grid = temp_factory.spatial_grid;
        debug!("Map loaded into EntitySystem");
    }

    /// Run the entity system update (spawners, spatial grid, etc.)
    pub fn run(&self) {
        let now = Instant::now();

        // Check if we should update movement (every 50ms like C++ code)
        let should_update_movement = {
            let mut last_movement_update = self.last_movement_update.lock().unwrap();
            if now.duration_since(*last_movement_update) >= Duration::from_millis(50) {
                *last_movement_update = now;
                true
            } else {
                false
            }
        };

        let mut world = self.world.lock().unwrap();
        let mut spatial_grid = self.spatial_grid.lock().unwrap();
        let id_manager = self.id_manager.clone();

        // Create a temporary factory to run updates
        let mut temp_factory = crate::entity_factory::EntityFactory {
            world: &mut *world,
            spatial_grid: std::mem::take(&mut *spatial_grid),
            id_manager,
            world_service: self.world_service.clone(),
        };

        // Always update spawners - they have their own timing with spawn_rate
        temp_factory.update_spawners();

        // Always update spatial grid
        temp_factory.update_spatial_grid();

        // Update movement every 50ms like C++ code
        if should_update_movement {
            temp_factory.update_movement(50); // 50ms delta time
        }

        // Put the spatial grid back
        *spatial_grid = temp_factory.spatial_grid;
    }

    /// Get nearby objects for a specific client
    pub fn get_nearby_objects_for_client(&self, client_id: u16, x: f32, y: f32, z: f32, map_id: u16) -> Vec<EntityInfo> {
        let world = self.world.lock().unwrap();
        let spatial_grid = self.spatial_grid.lock().unwrap();

        // Find the client entity to exclude from results
        let client_entity = world.query::<(&crate::components::client::Client)>().iter().find_map(|(entity, (client))| {
            if client.client_id == client_id {
                Some(entity)
            } else {
                None
            }
        });

        // Create a temporary position for the query
        let query_position = Position {
            x,
            y,
            z,
            map_id,
            spawn_id: 0,
        };

        // Get nearby entities using the spatial grid, excluding the client entity
        let nearby_entities = spatial_grid.get_nearby_entities(
            &*world,
            client_entity, // Exclude the client entity itself
            &query_position,
            map_id
        );

        debug!("Found {} nearby entities for client {} at position ({}, {}, {}) on map {}",
               nearby_entities.len(), client_id, x, y, z, map_id);

        // Convert entities to EntityInfo
        let mut entity_infos = Vec::new();
        for entity in nearby_entities {
            if let Some(info) = self.entity_to_info(&*world, entity) {
                entity_infos.push(info);
            }
        }

        entity_infos
    }

    /// Get nearby objects at a specific position
    pub fn get_nearby_objects_at_position(&self, x: f32, y: f32, z: f32, map_id: u16) -> Vec<EntityInfo> {
        let world = self.world.lock().unwrap();
        let spatial_grid = self.spatial_grid.lock().unwrap();

        // Create a temporary position for the query
        let query_position = Position {
            x,
            y,
            z,
            map_id,
            spawn_id: 0,
        };

        // Get nearby entities using the spatial grid
        let nearby_entities = spatial_grid.get_nearby_entities(
            &*world,
            None, // No query entity to exclude
            &query_position,
            map_id
        );

        debug!("Found {} nearby entities at position ({}, {}, {}) on map {}",
               nearby_entities.len(), x, y, z, map_id);

        // Convert entities to EntityInfo
        let mut entity_infos = Vec::new();
        for entity in nearby_entities {
            if let Some(info) = self.entity_to_info(&*world, entity) {
                entity_infos.push(info);
            }
        }

        entity_infos
    }

    /// Convert an entity to EntityInfo with all relevant data
    fn entity_to_info(&self, world: &World, entity: Entity) -> Option<EntityInfo> {
        // Get position (required)
        let position = world.get::<&Position>(entity).ok()?;
        
        // Get basic info (required)
        let basic_info = world.get::<&BasicInfo>(entity).ok()?;
        
        // Get life info for HP
        let (hp, max_hp) = if let Ok(life) = world.get::<&Life>(entity) {
            (life.get_hp() as i32, life.get_max_hp() as i32)
        } else {
            (100, 100)
        };

        // Determine entity type based on marker components
        let entity_type = if world.get::<&Player>(entity).is_ok() {
            EntityType::Player
        } else if world.get::<&Npc>(entity).is_ok() {
            EntityType::Npc
        } else if world.get::<&Mob>(entity).is_ok() {
            EntityType::Mob
        } else {
            return None; // Skip entities without recognized types
        };

        Some(EntityInfo {
            id: basic_info.id as i32,
            entity_type,
            x: position.x,
            y: position.y,
            z: position.z,
            name: basic_info.name.clone(),
            hp,
            max_hp,
        })
    }

    /// Get a clone of the world for read-only operations
    pub fn get_world(&self) -> Arc<Mutex<World>> {
        self.world.clone()
    }

    pub fn set_destination(&self, client_id: u16, x: f32, y: f32, z: f32) {
        use crate::components::client::Client;

        let mut world = self.world.lock().unwrap();

        // Find the player entity and get their current position
        let player_entity = world.query::<(&Client, &Position)>().iter().find_map(|(entity, (client, position))| {
            if client.client_id == client_id {
                Some((entity, position.clone()))
            } else {
                None
            }
        });

        if let Some((entity, current_pos)) = player_entity {
            // Calculate distance to new position
            let distance = ((x - current_pos.x).powi(2) + (y - current_pos.y).powi(2)).sqrt();

            // Create a Destination component for the player
            let destination = Destination::new(x, y, z, distance);
            world.insert_one(entity, destination).unwrap();
        }
    }



    /// Update player position and check for nearby entity changes
    pub fn update_player_position(&self, client_id: u16, x: f32, y: f32, z: f32, map_id: u16) {
        use crate::components::client::Client;

        let mut world = self.world.lock().unwrap();
        let mut spatial_grid = self.spatial_grid.lock().unwrap();

        // First, find the player entity
        let player_entity = {
            let mut query = world.query::<&Client>();
            query.iter().find_map(|(entity, client)| {
                if client.client_id == client_id {
                    Some(entity)
                } else {
                    None
                }
            })
        };

        if let Some(entity) = player_entity {
            // Get the current position to remove from spatial grid
            let old_position = {
                if let Ok(pos) = world.get::<&Position>(entity) {
                    pos.clone()
                } else {
                    debug!("Player entity {} has no position component", client_id);
                    return;
                }
            };

            // Update the spatial grid - remove from old position
            spatial_grid.remove_entity(entity, &old_position);

            // Update the entity's position component
            let new_position = Position {
                x,
                y,
                z,
                map_id,
                spawn_id: old_position.spawn_id,
            };

            // Drop the old_position to release the immutable borrow
            drop(old_position);

            if let Err(e) = world.insert_one(entity, new_position.clone()) {
                debug!("Failed to update position for player {}: {:?}", client_id, e);
                return;
            }

            // Add to spatial grid at new position
            spatial_grid.add_entity(entity, &new_position);

            debug!("Updated position for player {} to ({}, {}, {})", client_id, x, y, z);
        } else {
            debug!("Could not find player entity for client_id: {}", client_id);
        }
    }


}

/// Information about an entity that can be sent to clients
#[derive(Debug, Clone)]
pub struct EntityInfo {
    pub id: i32,
    pub entity_type: EntityType,
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub name: String,
    pub hp: i32,
    pub max_hp: i32,
}

/// Type of entity
#[derive(Debug, Clone)]
pub enum EntityType {
    Player = 1,
    Npc = 2,
    Mob = 3,
}
