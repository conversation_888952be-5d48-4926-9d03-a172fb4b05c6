#!/bin/bash

# MMORPG Server Environment Setup Script
# This script sets up environment variables for local development

set -e

echo "Setting up MMORPG Server environment variables..."

# Database Service Configuration
export DATABASE_URL=${DATABASE_URL:-"postgres://postgres:password@localhost:5432/mmorpg"}
export REDIS_URL=${REDIS_URL:-"redis://localhost:6379"}

# Service Ports
export DATABASE_SERVICE_PORT=${DATABASE_SERVICE_PORT:-"50052"}
export PACKET_SERVICE_PORT=${PACKET_SERVICE_PORT:-"29000"}
export WORLD_SERVICE_PORT=${WORLD_SERVICE_PORT:-"50053"}
export GAME_LOGIC_SERVICE_PORT=${GAME_LOGIC_SERVICE_PORT:-"50056"}

# Service Addresses
export LISTEN_ADDR=${LISTEN_ADDR:-"0.0.0.0"}

# Packet Service Configuration
export PACKET_METRICS_PORT=${PACKET_METRICS_PORT:-"4001"}

# World Service Configuration
export WORLD_SERVICE_NAME=${WORLD_SERVICE_NAME:-"world-service"}
export MAP_IDS=${MAP_IDS:-"42,43,44,45"}

# Game Logic Service Configuration
export MAP_ID=${MAP_ID:-"42"}

# Service Discovery (for local development)
export AUTH_SERVICE_URL=${AUTH_SERVICE_URL:-"http://localhost:50051"}
export CHARACTER_SERVICE_URL=${CHARACTER_SERVICE_URL:-"http://localhost:50054"}
export CHAT_SERVICE_URL=${CHAT_SERVICE_URL:-"http://localhost:50055"}
export DATABASE_SERVICE_URL=${DATABASE_SERVICE_URL:-"http://localhost:${DATABASE_SERVICE_PORT}"}
export WORLD_SERVICE_URL=${WORLD_SERVICE_URL:-"http://localhost:${WORLD_SERVICE_PORT}"}
export GAME_LOGIC_SERVICE_URL=${GAME_LOGIC_SERVICE_URL:-"http://localhost:${GAME_LOGIC_SERVICE_PORT}"}

# Logging Configuration
export RUST_LOG=${RUST_LOG:-"info,mmorpg_server=debug"}

# Connection Retry Configuration
export CONNECTION_INFO_MAX_RETRIES=${CONNECTION_INFO_MAX_RETRIES:-"3"}
export CONNECTION_INFO_INITIAL_DELAY_MS=${CONNECTION_INFO_INITIAL_DELAY_MS:-"2000"}
export CONNECTION_INFO_MAX_DELAY_MS=${CONNECTION_INFO_MAX_DELAY_MS:-"10000"}

# Game Logic Retry Configuration
export GAME_LOGIC_MAX_RETRIES=${GAME_LOGIC_MAX_RETRIES:-"3"}
export GAME_LOGIC_INITIAL_DELAY_MS=${GAME_LOGIC_INITIAL_DELAY_MS:-"500"}
export GAME_LOGIC_MAX_DELAY_MS=${GAME_LOGIC_MAX_DELAY_MS:-"10000"}

echo "Environment variables set:"
echo "  DATABASE_URL: $DATABASE_URL"
echo "  REDIS_URL: $REDIS_URL"
echo "  DATABASE_SERVICE_PORT: $DATABASE_SERVICE_PORT"
echo "  PACKET_SERVICE_PORT: $PACKET_SERVICE_PORT"
echo "  WORLD_SERVICE_PORT: $WORLD_SERVICE_PORT"
echo "  GAME_LOGIC_SERVICE_PORT: $GAME_LOGIC_SERVICE_PORT"
echo "  MAP_IDS: $MAP_IDS"
echo "  MAP_ID: $MAP_ID"
echo ""

# Check if Redis is running
echo "Checking Redis connection..."
if redis-cli ping > /dev/null 2>&1; then
    echo "✓ Redis is running"
else
    echo "✗ Redis is not running. Please start Redis server:"
    echo "  redis-server"
    echo ""
fi

# Check if PostgreSQL is accessible
echo "Checking PostgreSQL connection..."
if pg_isready -d "$DATABASE_URL" > /dev/null 2>&1; then
    echo "✓ PostgreSQL is accessible"
else
    echo "✗ PostgreSQL is not accessible. Please check DATABASE_URL:"
    echo "  $DATABASE_URL"
    echo ""
fi

echo "Environment setup complete!"
echo ""
echo "To start services in order:"
echo "1. cd database-service && cargo run"
echo "2. cd game-logic-service && MAP_ID=42 cargo run"
echo "3. cd world-service && cargo run"
echo "4. cd packet-service && cargo run"
echo ""
echo "To run tests:"
echo "  cargo test --all"
echo "  cd tests && cargo test integration_tests"
