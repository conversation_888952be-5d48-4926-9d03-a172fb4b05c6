#!/bin/bash

# MMORPG Server Service Startup Script
# Starts all services in the correct order for development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
STARTUP_DELAY=3
LOG_DIR="logs"

# Create logs directory
mkdir -p $LOG_DIR

echo -e "${BLUE}=== MMORPG Server Startup ===${NC}"
echo ""

# Source environment variables
if [ -f "scripts/setup-env.sh" ]; then
    echo "Loading environment variables..."
    source scripts/setup-env.sh
    echo ""
fi

# Function to start a service
start_service() {
    local service_name=$1
    local service_dir=$2
    local extra_env=$3
    local port=$4
    
    echo -e "${YELLOW}Starting $service_name...${NC}"
    
    cd $service_dir
    
    # Start service in background with logging
    if [ -n "$extra_env" ]; then
        env $extra_env cargo run > "../$LOG_DIR/${service_name}.log" 2>&1 &
    else
        cargo run > "../$LOG_DIR/${service_name}.log" 2>&1 &
    fi
    
    local pid=$!
    echo "$pid" > "../$LOG_DIR/${service_name}.pid"
    
    cd ..
    
    # Wait for service to start
    echo -n "  Waiting for $service_name to start"
    for i in {1..30}; do
        if nc -z localhost $port 2>/dev/null; then
            echo -e " ${GREEN}✓${NC}"
            echo "  $service_name started (PID: $pid, Port: $port)"
            return 0
        fi
        echo -n "."
        sleep 1
    done
    
    echo -e " ${RED}✗${NC}"
    echo -e "${RED}Failed to start $service_name${NC}"
    echo "Check logs: $LOG_DIR/${service_name}.log"
    return 1
}

# Function to check if service is already running
check_service() {
    local service_name=$1
    local port=$2
    
    if nc -z localhost $port 2>/dev/null; then
        echo -e "${YELLOW}$service_name is already running on port $port${NC}"
        return 0
    fi
    return 1
}

# Function to stop all services
stop_services() {
    echo ""
    echo -e "${YELLOW}Stopping all services...${NC}"
    
    for pidfile in $LOG_DIR/*.pid; do
        if [ -f "$pidfile" ]; then
            local pid=$(cat "$pidfile")
            local service=$(basename "$pidfile" .pid)
            
            if kill -0 $pid 2>/dev/null; then
                echo "  Stopping $service (PID: $pid)"
                kill $pid
                sleep 1
                
                # Force kill if still running
                if kill -0 $pid 2>/dev/null; then
                    echo "  Force stopping $service"
                    kill -9 $pid
                fi
            fi
            
            rm -f "$pidfile"
        fi
    done
    
    echo -e "${GREEN}All services stopped${NC}"
}

# Trap to stop services on script exit
trap stop_services EXIT

# Check prerequisites
echo "Checking prerequisites..."

# Check Redis
if ! redis-cli ping > /dev/null 2>&1; then
    echo -e "${RED}✗ Redis is not running${NC}"
    echo "Please start Redis: redis-server"
    exit 1
fi
echo -e "${GREEN}✓ Redis is running${NC}"

# Check PostgreSQL
if ! pg_isready -d "$DATABASE_URL" > /dev/null 2>&1; then
    echo -e "${RED}✗ PostgreSQL is not accessible${NC}"
    echo "Please check DATABASE_URL: $DATABASE_URL"
    exit 1
fi
echo -e "${GREEN}✓ PostgreSQL is accessible${NC}"

echo ""

# Start services in order
echo "Starting services..."
echo ""

# 1. Database Service
if ! check_service "Database Service" 50052; then
    start_service "database-service" "database-service" "" 50052 || exit 1
    sleep $STARTUP_DELAY
fi

# 2. Game Logic Service (Map 42)
if ! check_service "Game Logic Service" 50056; then
    start_service "game-logic-service" "game-logic-service" "MAP_ID=42" 50056 || exit 1
    sleep $STARTUP_DELAY
fi

# 3. World Service
if ! check_service "World Service" 50053; then
    start_service "world-service" "world-service" "" 50053 || exit 1
    sleep $STARTUP_DELAY
fi

# 4. Packet Service
if ! check_service "Packet Service" 29000; then
    start_service "packet-service" "packet-service" "" 29000 || exit 1
    sleep $STARTUP_DELAY
fi

echo ""
echo -e "${GREEN}=== All Services Started Successfully ===${NC}"
echo ""
echo "Service Status:"
echo "  Database Service:    http://localhost:50052"
echo "  Game Logic Service:  http://localhost:50056 (Map 42)"
echo "  World Service:       http://localhost:50053"
echo "  Packet Service:      tcp://localhost:29000"
echo ""
echo "Logs are available in: $LOG_DIR/"
echo ""
echo "To test the ID flow:"
echo "  ./scripts/test-id-flow.sh"
echo ""
echo "To stop all services:"
echo "  Press Ctrl+C or kill this script"
echo ""

# Keep script running
echo -e "${BLUE}Services are running. Press Ctrl+C to stop all services.${NC}"
while true; do
    sleep 10
    
    # Check if all services are still running
    all_running=true
    
    for pidfile in $LOG_DIR/*.pid; do
        if [ -f "$pidfile" ]; then
            local pid=$(cat "$pidfile")
            if ! kill -0 $pid 2>/dev/null; then
                local service=$(basename "$pidfile" .pid)
                echo -e "${RED}Warning: $service has stopped unexpectedly${NC}"
                all_running=false
            fi
        fi
    done
    
    if [ "$all_running" = false ]; then
        echo "Some services have stopped. Check logs in $LOG_DIR/"
        break
    fi
done
