#!/bin/bash

# End-to-End ID Flow Testing Script
# Tests the complete ID handling flow across all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Service endpoints
DATABASE_SERVICE="localhost:50052"
WORLD_SERVICE="localhost:50053"
GAME_LOGIC_SERVICE="localhost:50056"

echo -e "${BLUE}=== MMORPG Server ID Flow Test ===${NC}"
echo ""

# Check if services are running
check_service() {
    local service=$1
    local port=$2
    echo -n "Checking $service... "
    if nc -z localhost $port 2>/dev/null; then
        echo -e "${GREEN}✓ Running${NC}"
        return 0
    else
        echo -e "${RED}✗ Not running${NC}"
        return 1
    fi
}

echo "Checking service availability:"
check_service "Database Service" 50052 || { echo "Please start database-service"; exit 1; }
check_service "World Service" 50053 || { echo "Please start world-service"; exit 1; }
check_service "Game Logic Service" 50056 || { echo "Please start game-logic-service"; exit 1; }
echo ""

# Generate test data
CLIENT_ID=$(uuidgen | tr '[:upper:]' '[:lower:]')
USER_ID="testuser_$(date +%s)"
MAP_ID=42
SPAWN_X=100.0
SPAWN_Y=200.0
SPAWN_Z=0.0

echo -e "${YELLOW}Test Data:${NC}"
echo "  Client ID: $CLIENT_ID"
echo "  User ID: $USER_ID"
echo "  Map ID: $MAP_ID"
echo ""

# Step 1: Store client mapping (simulates packet service)
echo -e "${BLUE}Step 1: Store client mapping${NC}"
CLIENT_RESPONSE=$(grpcurl -plaintext -d "{
  \"client_id\": \"$CLIENT_ID\",
  \"connection_info\": \"{\\\"peer_addr\\\": \\\"127.0.0.1:12345\\\", \\\"connected_at\\\": \\\"$(date -Iseconds)\\\"}\",
  \"created_at\": $(date +%s)
}" $DATABASE_SERVICE client_management.ClientManagementService/SetClient 2>/dev/null)

if echo "$CLIENT_RESPONSE" | grep -q '"success": true'; then
    echo -e "${GREEN}✓ Client mapping stored successfully${NC}"
else
    echo -e "${RED}✗ Failed to store client mapping${NC}"
    echo "$CLIENT_RESPONSE"
    exit 1
fi

# Step 2: Validate client
echo -e "${BLUE}Step 2: Validate client${NC}"
VALIDATE_RESPONSE=$(grpcurl -plaintext -d "{
  \"client_id\": \"$CLIENT_ID\"
}" $DATABASE_SERVICE client_management.ClientManagementService/ValidateClient 2>/dev/null)

if echo "$VALIDATE_RESPONSE" | grep -q '"valid": true'; then
    echo -e "${GREEN}✓ Client validation successful${NC}"
else
    echo -e "${RED}✗ Client validation failed${NC}"
    echo "$VALIDATE_RESPONSE"
    exit 1
fi

# Step 3: Client handshake (creates session)
echo -e "${BLUE}Step 3: Client handshake${NC}"
HANDSHAKE_RESPONSE=$(grpcurl -plaintext -d "{
  \"client_id\": \"$CLIENT_ID\",
  \"user_id\": \"$USER_ID\"
}" $WORLD_SERVICE world.WorldService/ClientHandshake 2>/dev/null)

if echo "$HANDSHAKE_RESPONSE" | grep -q '"success": true'; then
    SESSION_ID=$(echo "$HANDSHAKE_RESPONSE" | grep -o '"sessionId": "[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✓ Handshake successful${NC}"
    echo "  Session ID: $SESSION_ID"
else
    echo -e "${RED}✗ Handshake failed${NC}"
    echo "$HANDSHAKE_RESPONSE"
    exit 1
fi

# Step 4: Join map (creates entity)
echo -e "${BLUE}Step 4: Join map${NC}"
JOIN_RESPONSE=$(grpcurl -plaintext -d "{
  \"session_id\": \"$SESSION_ID\",
  \"map_id\": $MAP_ID,
  \"spawn_x\": $SPAWN_X,
  \"spawn_y\": $SPAWN_Y,
  \"spawn_z\": $SPAWN_Z
}" $WORLD_SERVICE world.WorldService/JoinMap 2>/dev/null)

if echo "$JOIN_RESPONSE" | grep -q '"success": true'; then
    ENTITY_ID=$(echo "$JOIN_RESPONSE" | grep -o '"entityId": "[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✓ Map join successful${NC}"
    echo "  Entity ID: $ENTITY_ID"
else
    echo -e "${RED}✗ Map join failed${NC}"
    echo "$JOIN_RESPONSE"
    exit 1
fi

# Step 5: Verify entity exists
echo -e "${BLUE}Step 5: Verify entity creation${NC}"
LIST_RESPONSE=$(grpcurl -plaintext -d "{
  \"map_id\": $MAP_ID
}" $GAME_LOGIC_SERVICE entity_management.EntityManagementService/ListEntities 2>/dev/null)

if echo "$LIST_RESPONSE" | grep -q "\"entityId\": \"$ENTITY_ID\""; then
    echo -e "${GREEN}✓ Entity found in map${NC}"
else
    echo -e "${RED}✗ Entity not found in map${NC}"
    echo "$LIST_RESPONSE"
    exit 1
fi

# Step 6: Get specific entity
echo -e "${BLUE}Step 6: Get entity details${NC}"
GET_ENTITY_RESPONSE=$(grpcurl -plaintext -d "{
  \"entity_id\": $ENTITY_ID,
  \"map_id\": $MAP_ID
}" $GAME_LOGIC_SERVICE entity_management.EntityManagementService/GetEntity 2>/dev/null)

if echo "$GET_ENTITY_RESPONSE" | grep -q '"found": true'; then
    echo -e "${GREEN}✓ Entity details retrieved${NC}"
else
    echo -e "${RED}✗ Failed to get entity details${NC}"
    echo "$GET_ENTITY_RESPONSE"
    exit 1
fi

# Step 7: Test Redis storage
echo -e "${BLUE}Step 7: Verify Redis storage${NC}"
echo "Checking Redis keys..."

# Check client mapping
if redis-cli EXISTS "client:$CLIENT_ID" | grep -q "1"; then
    echo -e "${GREEN}✓ Client mapping exists in Redis${NC}"
    CLIENT_TTL=$(redis-cli TTL "client:$CLIENT_ID")
    echo "  Client TTL: ${CLIENT_TTL}s"
else
    echo -e "${RED}✗ Client mapping not found in Redis${NC}"
fi

# Check session mapping
if redis-cli EXISTS "session:$SESSION_ID" | grep -q "1"; then
    echo -e "${GREEN}✓ Session mapping exists in Redis${NC}"
    SESSION_TTL=$(redis-cli TTL "session:$SESSION_ID")
    echo "  Session TTL: ${SESSION_TTL}s"
else
    echo -e "${RED}✗ Session mapping not found in Redis${NC}"
fi

# Step 8: Test cleanup (leave map)
echo -e "${BLUE}Step 8: Test cleanup (leave map)${NC}"
LEAVE_RESPONSE=$(grpcurl -plaintext -d "{
  \"client_id\": \"$CLIENT_ID\",
  \"map_id\": $MAP_ID
}" $GAME_LOGIC_SERVICE entity_management.EntityManagementService/LeaveMap 2>/dev/null)

if echo "$LEAVE_RESPONSE" | grep -q '"success": true'; then
    echo -e "${GREEN}✓ Successfully left map${NC}"
else
    echo -e "${RED}✗ Failed to leave map${NC}"
    echo "$LEAVE_RESPONSE"
fi

echo ""
echo -e "${GREEN}=== ID Flow Test Complete ===${NC}"
echo ""
echo -e "${YELLOW}Summary:${NC}"
echo "  Client ID: $CLIENT_ID"
echo "  Session ID: $SESSION_ID"
echo "  Entity ID: $ENTITY_ID"
echo "  Map ID: $MAP_ID"
echo ""
echo -e "${BLUE}All ID handling components working correctly!${NC}"
