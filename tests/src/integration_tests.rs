#[cfg(test)]
mod tests {
    use std::time::Duration;
    use tokio::time::sleep;
    use uuid::Uuid;

    // Test client ID generation
    #[test]
    fn test_client_id_generation() {
        let id1 = Uuid::new_v4().to_string();
        let id2 = Uuid::new_v4().to_string();
        
        // Should be different UUIDs
        assert_ne!(id1, id2);
        
        // Should be valid UUID format
        assert!(Uuid::parse_str(&id1).is_ok());
        assert!(Uuid::parse_str(&id2).is_ok());
        
        // Should be 36 characters long (including hyphens)
        assert_eq!(id1.len(), 36);
        assert_eq!(id2.len(), 36);
    }

    // Test session ID generation
    #[test]
    fn test_session_id_generation() {
        let session_id1 = Uuid::new_v4().to_string();
        let session_id2 = Uuid::new_v4().to_string();
        
        // Should be different UUIDs
        assert_ne!(session_id1, session_id2);
        
        // Should be valid UUID format
        assert!(Uuid::parse_str(&session_id1).is_ok());
        assert!(Uuid::parse_str(&session_id2).is_ok());
    }

    // Test entity ID generation (AtomicU64)
    #[test]
    fn test_entity_id_generation() {
        use std::sync::atomic::{AtomicU64, Ordering};
        
        let counter = AtomicU64::new(1);
        
        let id1 = counter.fetch_add(1, Ordering::SeqCst);
        let id2 = counter.fetch_add(1, Ordering::SeqCst);
        let id3 = counter.fetch_add(1, Ordering::SeqCst);
        
        // Should be sequential
        assert_eq!(id1, 1);
        assert_eq!(id2, 2);
        assert_eq!(id3, 3);
        
        // Should be unique
        assert_ne!(id1, id2);
        assert_ne!(id2, id3);
        assert_ne!(id1, id3);
    }

    // Test Redis key schema
    #[test]
    fn test_redis_key_schema() {
        let client_id = "550e8400-e29b-41d4-a716-************";
        let session_id = "6ba7b810-9dad-11d1-80b4-00c04fd430c8";
        let map_id = 42u32;
        
        // Test key formats
        let client_key = format!("client:{}", client_id);
        let session_key = format!("session:{}", session_id);
        let entity_key = format!("entity:{}:{}", map_id, client_id);
        
        assert_eq!(client_key, "client:550e8400-e29b-41d4-a716-************");
        assert_eq!(session_key, "session:6ba7b810-9dad-11d1-80b4-00c04fd430c8");
        assert_eq!(entity_key, "entity:42:550e8400-e29b-41d4-a716-************");
    }

    // Test ID flow simulation
    #[tokio::test]
    async fn test_id_flow_simulation() {
        // Simulate the complete ID flow
        
        // 1. Packet service generates client_id
        let client_id = Uuid::new_v4().to_string();
        assert!(Uuid::parse_str(&client_id).is_ok());
        
        // 2. World service validates client_id and creates session_id
        let session_id = Uuid::new_v4().to_string();
        assert!(Uuid::parse_str(&session_id).is_ok());
        
        // 3. Game logic service generates entity_id
        use std::sync::atomic::{AtomicU64, Ordering};
        let entity_counter = AtomicU64::new(1);
        let entity_id = entity_counter.fetch_add(1, Ordering::SeqCst);
        
        // 4. Verify all IDs are unique and valid
        assert_ne!(client_id, session_id);
        assert!(entity_id > 0);
        
        // 5. Simulate map change - new entity_id for same client
        let new_entity_id = entity_counter.fetch_add(1, Ordering::SeqCst);
        assert_ne!(entity_id, new_entity_id);
        assert_eq!(new_entity_id, entity_id + 1);
    }

    // Test concurrent entity ID generation
    #[tokio::test]
    async fn test_concurrent_entity_id_generation() {
        use std::sync::atomic::{AtomicU64, Ordering};
        use std::sync::Arc;
        use tokio::task::JoinSet;
        
        let counter = Arc::new(AtomicU64::new(1));
        let mut join_set = JoinSet::new();
        
        // Spawn 10 concurrent tasks to generate entity IDs
        for _ in 0..10 {
            let counter_clone = counter.clone();
            join_set.spawn(async move {
                counter_clone.fetch_add(1, Ordering::SeqCst)
            });
        }
        
        let mut ids = Vec::new();
        while let Some(result) = join_set.join_next().await {
            ids.push(result.unwrap());
        }
        
        // All IDs should be unique
        ids.sort();
        for i in 1..ids.len() {
            assert_ne!(ids[i-1], ids[i]);
        }
        
        // Should have 10 unique IDs
        assert_eq!(ids.len(), 10);
    }

    // Test TTL behavior simulation
    #[tokio::test]
    async fn test_ttl_simulation() {
        use std::collections::HashMap;
        use std::time::{SystemTime, UNIX_EPOCH};
        
        // Simulate Redis TTL behavior
        let mut cache: HashMap<String, (String, u64)> = HashMap::new();
        let client_id = Uuid::new_v4().to_string();
        let connection_info = r#"{"peer_addr": "127.0.0.1:12345", "connected_at": "2023-01-01T00:00:00Z"}"#;
        
        // Set with TTL (simulated as expiry timestamp)
        let ttl_seconds = 3600; // 1 hour
        let expiry = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs() + ttl_seconds;
        cache.insert(format!("client:{}", client_id), (connection_info.to_string(), expiry));
        
        // Verify entry exists
        assert!(cache.contains_key(&format!("client:{}", client_id)));
        
        // Simulate TTL check
        let current_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
        let key = format!("client:{}", client_id);
        if let Some((_, expiry_time)) = cache.get(&key) {
            assert!(current_time < *expiry_time); // Should not be expired
        }
    }

    // Test error handling scenarios
    #[test]
    fn test_error_scenarios() {
        // Test invalid UUID parsing
        assert!(Uuid::parse_str("invalid-uuid").is_err());
        assert!(Uuid::parse_str("").is_err());
        assert!(Uuid::parse_str("123").is_err());
        
        // Test valid UUID parsing
        assert!(Uuid::parse_str("550e8400-e29b-41d4-a716-************").is_ok());
    }

    // Test map ID validation
    #[test]
    fn test_map_id_validation() {
        let valid_map_ids = vec![1u32, 42u32, 100u32, 999u32];
        let invalid_map_ids = vec![0u32]; // Assuming 0 is invalid
        
        for map_id in valid_map_ids {
            assert!(map_id > 0);
        }
        
        for map_id in invalid_map_ids {
            assert_eq!(map_id, 0);
        }
    }

    // Test client-entity mapping
    #[test]
    fn test_client_entity_mapping() {
        use std::collections::HashMap;
        
        let mut mappings: HashMap<(u32, String), u64> = HashMap::new();
        
        let client_id1 = Uuid::new_v4().to_string();
        let client_id2 = Uuid::new_v4().to_string();
        let map_id = 42u32;
        
        // Map clients to entities
        mappings.insert((map_id, client_id1.clone()), 1001);
        mappings.insert((map_id, client_id2.clone()), 1002);
        
        // Verify mappings
        assert_eq!(mappings.get(&(map_id, client_id1.clone())), Some(&1001));
        assert_eq!(mappings.get(&(map_id, client_id2.clone())), Some(&1002));
        
        // Test map change - same client, different map, new entity
        let new_map_id = 43u32;
        mappings.insert((new_map_id, client_id1.clone()), 2001);
        
        // Client should have different entities on different maps
        assert_eq!(mappings.get(&(map_id, client_id1.clone())), Some(&1001));
        assert_eq!(mappings.get(&(new_map_id, client_id1.clone())), Some(&2001));
    }
}
