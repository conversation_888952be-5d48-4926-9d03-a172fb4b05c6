# MMORPG Server Architecture

A microservice-based server architecture for an MMORPG game, built with Rust with end-to-end ID handling.

## Overview

This project implements a complete server infrastructure for an MMORPG game, using a microservice architecture for scalability and maintainability. Each service is responsible for a specific domain of the game, communicating with other services via gRPC.

## Architecture

The server architecture consists of the following microservices:

### Core Services

- **Packet Service**: Handles game client communication and generates globally unique client IDs (UUID v4)
- **Database Service**: Provides centralized database access and Redis-based ID mapping storage
- **World Service**: Manages client handshakes, session creation, and map join coordination
- **Game Logic Service**: Manages per-map entity state with unique entity IDs (AtomicU64)
- **Auth Service**: Handles user authentication, session validation, and account management
- **Character Service**: Manages character creation, deletion, and retrieval

### Support Components

- **Utils**: Shared utilities used by all services
- **Launcher**: Client-side launcher for the game

## End-to-End ID Handling

### ID Flow Architecture

1. **Client Connection**: Packet Service generates UUID v4 `client_id` and stores in Database Service
2. **Client Handshake**: World Service validates `client_id` and creates `session_id` (UUID v4)
3. **Map Join**: World Service coordinates with Game Logic Service to assign `entity_id` (AtomicU64)
4. **Entity Management**: Game Logic Service manages per-map entities with unique IDs

### ID Types and Responsibilities

- **client_id** (UUID v4): Generated by Packet Service, globally unique, persisted in Redis
- **session_id** (UUID v4): Generated by World Service, maps client to user, persisted in Redis
- **entity_id** (AtomicU64): Generated by Game Logic Service, unique per map instance

### Redis Key Schema

- `client:{client_id}` → Client connection metadata (TTL: 1 hour)
- `session:{session_id}` → Session mapping to client_id and user_id (TTL: 1 hour)
- `entity:{map_id}:{client_id}` → Entity ID mapping (TTL: 30 minutes)

## Communication Flow

1. **Client → Packet Service**: Client connects, receives `client_id`
2. **Packet Service → Database Service**: Stores client mapping
3. **Client → World Service**: Handshake with `client_id`, receives `session_id`
4. **World Service → Database Service**: Validates client, stores session
5. **Client → World Service**: Join map with `session_id`
6. **World Service → Game Logic Service**: Assign entity, receives `entity_id`
7. **Game Logic Service**: Manages entity state per map

## Technologies

- **Language**: Rust
- **Communication**: gRPC, custom binary protocol
- **Database**: PostgreSQL (managed by external system)
- **Caching**: Redis (ID mappings and session storage)
- **Service Discovery**: Kubernetes DNS, Consul
- **Metrics**: Prometheus
- **ID Generation**: UUID v4 (client/session), AtomicU64 (entities)

## Getting Started

### Prerequisites

- Rust (latest stable)
- PostgreSQL
- Redis
- Protobuf compiler

### Environment Variables

Each service requires specific environment variables:

#### Database Service
```bash
export DATABASE_URL=postgres://username:password@localhost:5432/dbname
export REDIS_URL=redis://localhost:6379
export LISTEN_ADDR=0.0.0.0
export SERVICE_PORT=50052
```

#### Packet Service
```bash
export LISTEN_ADDR=0.0.0.0
export SERVICE_PORT=29000
export PACKET_METRICS_PORT=4001
```

#### World Service
```bash
export LISTEN_ADDR=0.0.0.0
export SERVICE_PORT=50053
export WORLD_SERVICE_NAME=world-service
export MAP_IDS=42,43,44,45
```

#### Game Logic Service
```bash
export LISTEN_ADDR=0.0.0.0
export SERVICE_PORT=50056
export MAP_ID=42
```

### Building

Build all services:
```bash
cargo build --release
```

Build individual services:
```bash
# Database Service
cd database-service && cargo build --release

# Packet Service
cd packet-service && cargo build --release

# World Service
cd world-service && cargo build --release

# Game Logic Service
cd game-logic-service && cargo build --release
```

### Running Services

Start services in the following order:

1. **Database Service** (provides ID storage):
```bash
cd database-service && cargo run
```

2. **Game Logic Service** (one per map):
```bash
cd game-logic-service && MAP_ID=42 cargo run
```

3. **World Service** (coordinates handshakes and map joins):
```bash
cd world-service && cargo run
```

4. **Packet Service** (handles client connections):
```bash
cd packet-service && cargo run
```

### Docker

Each service includes a Dockerfile for containerized deployment:

```bash
# Build and run with Docker Compose
docker-compose up --build

# Or build individual services
docker build -t database-service ./database-service
docker build -t packet-service ./packet-service
docker build -t world-service ./world-service
docker build -t game-logic-service ./game-logic-service
```

## Documentation

Each service includes its own README.md with detailed documentation:

- [Auth Service](auth-service/README.md)
- [Character Service](character-service/README.md)
- [Database Service](database-service/README.md)
- [Packet Service](packet-service/README.md)
- [World Service](world-service/README.md)
- [Utils](utils/README.md)
- [Launcher](launcher/README.md)

## Testing

### Unit Tests

Run tests for all services:
```bash
cargo test --all
```

Run tests for individual services:
```bash
# Database Service tests
cd database-service && cargo test

# Packet Service tests
cd packet-service && cargo test

# World Service tests
cd world-service && cargo test

# Game Logic Service tests
cd game-logic-service && cargo test

# Integration tests
cd tests && cargo test
```

### Integration Testing

Test the complete ID flow:

1. **Start Redis** (required for database service):
```bash
redis-server
```

2. **Start PostgreSQL** (required for database service):
```bash
# Configure DATABASE_URL environment variable
export DATABASE_URL=postgres://username:password@localhost:5432/dbname
```

3. **Run integration tests**:
```bash
cd tests && cargo test integration_tests
```

### Manual Testing with grpcurl

#### Test Database Service (Client Management)
```bash
# Set client mapping
grpcurl -plaintext -d '{
  "client_id": "550e8400-e29b-41d4-a716-************",
  "connection_info": "{\"peer_addr\": \"127.0.0.1:12345\"}",
  "created_at": 1640995200
}' localhost:50052 client_management.ClientManagementService/SetClient

# Get client mapping
grpcurl -plaintext -d '{
  "client_id": "550e8400-e29b-41d4-a716-************"
}' localhost:50052 client_management.ClientManagementService/GetClient

# Validate client
grpcurl -plaintext -d '{
  "client_id": "550e8400-e29b-41d4-a716-************"
}' localhost:50052 client_management.ClientManagementService/ValidateClient
```

#### Test World Service (Handshake and Map Join)
```bash
# Client handshake
grpcurl -plaintext -d '{
  "client_id": "550e8400-e29b-41d4-a716-************",
  "user_id": "user123"
}' localhost:50053 world.WorldService/ClientHandshake

# Join map
grpcurl -plaintext -d '{
  "session_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
  "map_id": 42,
  "spawn_x": 100.0,
  "spawn_y": 200.0,
  "spawn_z": 0.0
}' localhost:50053 world.WorldService/JoinMap
```

#### Test Game Logic Service (Entity Management)
```bash
# Join map (creates player entity)
grpcurl -plaintext -d '{
  "client_id": "550e8400-e29b-41d4-a716-************",
  "map_id": 42,
  "spawn_x": 100.0,
  "spawn_y": 200.0,
  "spawn_z": 0.0
}' localhost:50056 entity_management.EntityManagementService/JoinMap

# List entities on map
grpcurl -plaintext -d '{
  "map_id": 42
}' localhost:50056 entity_management.EntityManagementService/ListEntities

# Get specific entity
grpcurl -plaintext -d '{
  "entity_id": 1,
  "map_id": 42
}' localhost:50056 entity_management.EntityManagementService/GetEntity
```

### Redis Inspection

Inspect stored ID mappings:

```bash
# Connect to Redis
redis-cli

# Check client mappings
KEYS client:*
GET client:550e8400-e29b-41d4-a716-************

# Check session mappings
KEYS session:*
GET session:6ba7b810-9dad-11d1-80b4-00c04fd430c8

# Check entity mappings
KEYS entity:*
GET entity:42:550e8400-e29b-41d4-a716-************

# Check TTL values
TTL client:550e8400-e29b-41d4-a716-************
TTL session:6ba7b810-9dad-11d1-80b4-00c04fd430c8
```

### End-to-End Flow Testing

Complete flow test:

1. **Generate client_id** (simulated packet service):
```bash
# This would normally be done by packet service on client connection
CLIENT_ID=$(uuidgen)
echo "Generated client_id: $CLIENT_ID"
```

2. **Store client mapping**:
```bash
grpcurl -plaintext -d "{
  \"client_id\": \"$CLIENT_ID\",
  \"connection_info\": \"{\\\"peer_addr\\\": \\\"127.0.0.1:12345\\\"}\",
  \"created_at\": $(date +%s)
}" localhost:50052 client_management.ClientManagementService/SetClient
```

3. **Client handshake**:
```bash
SESSION_RESPONSE=$(grpcurl -plaintext -d "{
  \"client_id\": \"$CLIENT_ID\",
  \"user_id\": \"testuser\"
}" localhost:50053 world.WorldService/ClientHandshake)

SESSION_ID=$(echo "$SESSION_RESPONSE" | jq -r '.sessionId')
echo "Generated session_id: $SESSION_ID"
```

4. **Join map**:
```bash
grpcurl -plaintext -d "{
  \"session_id\": \"$SESSION_ID\",
  \"map_id\": 42,
  \"spawn_x\": 100.0,
  \"spawn_y\": 200.0,
  \"spawn_z\": 0.0
}" localhost:50053 world.WorldService/JoinMap
```

5. **Verify entity creation**:
```bash
grpcurl -plaintext -d '{
  "map_id": 42
}' localhost:50056 entity_management.EntityManagementService/ListEntities
```

## Design Decisions and Trade-offs

### ID Generation Strategy

#### Client IDs (UUID v4)
- **Choice**: UUID v4 for globally unique client identifiers
- **Rationale**: Ensures uniqueness across distributed systems without coordination
- **Trade-off**: 36-character strings vs. smaller numeric IDs (chose readability and uniqueness)

#### Session IDs (UUID v4)
- **Choice**: UUID v4 for session identifiers
- **Rationale**: Cryptographically secure, unpredictable, globally unique
- **Trade-off**: Storage overhead vs. security (chose security)

#### Entity IDs (AtomicU64)
- **Choice**: AtomicU64 for per-map entity IDs
- **Rationale**: Fast generation, thread-safe, sequential within map instance
- **Trade-off**: Per-map uniqueness vs. global uniqueness (chose performance)

### Storage Strategy

#### Redis for ID Mappings
- **Choice**: Redis for client, session, and entity mappings
- **Rationale**: Fast in-memory access, TTL support, persistence options
- **Trade-off**: Memory usage vs. performance (chose performance)

#### TTL Configuration
- **Client mappings**: 1 hour (connection lifetime)
- **Session mappings**: 1 hour (active session duration)
- **Entity mappings**: 30 minutes (map instance lifetime)
- **Rationale**: Balance between data retention and memory usage

### Entity Management

#### In-Memory vs. Persistent Storage
- **Choice**: In-memory entity state with optional Redis persistence
- **Rationale**: Game logic requires fast access, Redis provides backup
- **Trade-off**: Data loss on restart vs. performance (chose performance with backup)

#### Per-Map Entity Services
- **Choice**: Separate game logic service instance per map
- **Rationale**: Horizontal scaling, fault isolation, load distribution
- **Trade-off**: Resource usage vs. scalability (chose scalability)

### Service Communication

#### gRPC for Service-to-Service
- **Choice**: gRPC for all service communication
- **Rationale**: Type safety, performance, streaming support
- **Trade-off**: Complexity vs. reliability (chose reliability)

#### Synchronous ID Operations
- **Choice**: Synchronous calls for ID generation and validation
- **Rationale**: Consistency requirements, error handling
- **Trade-off**: Latency vs. consistency (chose consistency)

### Error Handling

#### Graceful Degradation
- **Choice**: Services continue operating with reduced functionality on dependency failures
- **Rationale**: System resilience, partial availability
- **Implementation**: Retry logic, circuit breakers, fallback responses

#### ID Collision Handling
- **Choice**: UUID v4 collision probability is negligible (1 in 2^122)
- **Rationale**: Mathematical probability makes collision handling unnecessary
- **Trade-off**: Collision detection overhead vs. probability (chose probability)

### Performance Considerations

#### Connection Pooling
- **Database connections**: Pooled with configurable limits
- **Redis connections**: Pooled via deadpool-redis
- **gRPC connections**: Reused with keep-alive

#### Caching Strategy
- **Client validation**: Cached in Redis with TTL
- **Session lookup**: Cached in Redis with TTL
- **Entity queries**: In-memory with optional Redis backup

### Security Considerations

#### ID Predictability
- **Client/Session IDs**: Cryptographically random (UUID v4)
- **Entity IDs**: Sequential but scoped to map instance
- **Rationale**: Balance between security and performance

#### Access Control
- **Client validation**: Required for all operations
- **Session validation**: Required for map operations
- **Entity access**: Scoped to map and client ownership

## Monitoring and Observability

### Metrics
- Connection counts and rates
- ID generation rates and latencies
- Service response times
- Error rates and types

### Logging
- Structured logging with tracing
- Request/response correlation
- Error context and stack traces

### Health Checks
- Service availability endpoints
- Dependency health monitoring
- Graceful shutdown handling

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.
